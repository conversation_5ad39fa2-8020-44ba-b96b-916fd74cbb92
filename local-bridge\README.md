# PAX Hardware Bridge Server

This local bridge server enables communication between your Vercel-deployed React app and the PAX A920PFG terminal.

## Setup

1. **Install Dependencies**
   ```bash
   cd local-bridge
   npm install
   ```

2. **Place PAX SDK**
   - Create a `libs` folder in this directory
   - Copy `PaxWrapperSDK.dll` to `local-bridge/libs/`

3. **Configure Terminal**
   - Terminal IP: `**************` (from your terminal info)
   - Port: `10009`
   - Model: A920PFG

## Running

### Development
```bash
npm run dev
```

### Production
```bash
npm start
```

The server will run on `http://localhost:3002`

## API Endpoints

- `GET /health` - Health check
- `POST /initialize` - Initialize terminal connection
- `POST /payment` - Process payment
- `POST /print-receipt` - Print receipt
- `GET /status` - Get terminal status

## Architecture

```
Vercel (React App) → Local Bridge Server → PAX Terminal
     (Cloud)              (Local)           (Hardware)
```

The React app deployed on Vercel communicates with this local bridge server, which then interfaces with the PAX hardware using the native SDK.

## CORS Configuration

The server is configured to accept requests from:
- `http://localhost:3000` (development)
- `https://*.vercel.app` (production)

## Usage with React App

The React app will automatically detect and connect to this bridge server when running locally alongside the hardware.
