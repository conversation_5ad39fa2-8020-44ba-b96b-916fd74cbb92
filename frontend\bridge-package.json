{"name": "pax-hardware-bridge", "version": "1.0.0", "description": "Node.js bridge service for PAX hardware integration", "main": "src/services/paxBridge.js", "scripts": {"start": "node src/services/paxBridge.js", "start:dev": "nodemon src/services/paxBridge.js", "install-deps": "npm install express cors edge-js", "test": "node src/services/paxBridge.js --port 3003"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "edge-js": "^20.10.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}, "os": ["win32"], "keywords": ["pax", "pos", "hardware", "bridge", "payment"], "author": "PaxSoft", "license": "MIT"}