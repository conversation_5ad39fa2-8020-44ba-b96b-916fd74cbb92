/**
 * PAX POS Terminal Component
 * Web-based interface for PAX A920 terminal functionality
 */

import { useState, useEffect, useRef } from 'react';
import { paxPosService, PaxPaymentRequest, PaxPaymentResponse, PaxTerminalStatus } from '../services/paxPosService';
import { useSuccessToast, useErrorToast, useWarningToast } from './ui/toast';
import { PaxReceiptModal } from './PaxReceiptViewer';

interface PaxPosTerminalProps {
  onPaymentComplete?: (result: PaxPaymentResponse) => void;
  onBack?: () => void;
  defaultAmount?: number;
}

interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
}

export function PaxPosTerminal({ onPaymentComplete, onBack, defaultAmount }: PaxPosTerminalProps) {
  const [amount, setAmount] = useState<string>(defaultAmount?.toString() || '');
  const [isProcessing, setIsProcessing] = useState(false);
  const [terminalStatus, setTerminalStatus] = useState<PaxTerminalStatus | null>(null);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [isConnecting, setIsConnecting] = useState(false);
  const [lastTransaction, setLastTransaction] = useState<PaxPaymentResponse | null>(null);
  const [showReceiptModal, setShowReceiptModal] = useState(false);
  const [isPrinting, setIsPrinting] = useState(false);
  
  const successToast = useSuccessToast();
  const errorToast = useErrorToast();
  const warningToast = useWarningToast();
  
  const amountInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    initializePaxService();
  }, []);

  const initializePaxService = async () => {
    setIsConnecting(true);
    try {
      await paxPosService.initialize();
      const status = await paxPosService.getTerminalStatus();
      setTerminalStatus(status);
      
      if (status.connected) {
        successToast('PAX terminal connected successfully');
      } else {
        warningToast('PAX terminal not connected - running in simulation mode');
      }
    } catch (error) {
      console.error('Failed to initialize PAX service:', error);
      errorToast('Failed to connect to PAX terminal');
    } finally {
      setIsConnecting(false);
    }
  };

  const handlePayment = async () => {
    const paymentAmount = parseFloat(amount);
    
    if (!paymentAmount || paymentAmount <= 0) {
      errorToast('Please enter a valid amount');
      amountInputRef.current?.focus();
      return;
    }

    setIsProcessing(true);
    
    try {
      const paymentRequest: PaxPaymentRequest = {
        amount: paymentAmount,
        tenderType: 'CREDIT',
        transType: 'SALE',
        items: cart.map(item => ({
          name: item.name,
          price: item.price,
          quantity: item.quantity
        }))
      };

      let result: PaxPaymentResponse;
      
      if (paxPosService.isSimulationMode()) {
        result = await paxPosService.simulatePayment(paymentRequest);
      } else {
        result = await paxPosService.processPayment(paymentRequest);
      }

      setLastTransaction(result);

      if (result.success) {
        successToast(`Payment successful! Transaction ID: ${result.transactionId}`);
        setAmount('');
        setCart([]);
        onPaymentComplete?.(result);
      } else {
        errorToast(`Payment failed: ${result.message}`);
      }
    } catch (error) {
      console.error('Payment processing error:', error);
      errorToast('Payment processing failed');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCancelTransaction = async () => {
    if (!isProcessing) return;
    
    try {
      const result = await paxPosService.cancelTransaction();
      if (result.success) {
        successToast('Transaction cancelled');
        setIsProcessing(false);
      } else {
        errorToast(`Cancel failed: ${result.message}`);
      }
    } catch (error) {
      console.error('Cancel transaction error:', error);
      errorToast('Failed to cancel transaction');
    }
  };

  const handleTestConnection = async () => {
    setIsConnecting(true);
    try {
      const result = await paxPosService.testConnection();
      if (result.success) {
        successToast('Connection test successful');
        await initializePaxService(); // Refresh status
      } else {
        errorToast(`Connection test failed: ${result.message}`);
      }
    } catch (error) {
      console.error('Connection test error:', error);
      errorToast('Connection test failed');
    } finally {
      setIsConnecting(false);
    }
  };

  const addToCart = (name: string, price: number) => {
    const existingItem = cart.find(item => item.name === name);
    if (existingItem) {
      setCart(cart.map(item => 
        item.name === name 
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ));
    } else {
      setCart([...cart, {
        id: Date.now().toString(),
        name,
        price,
        quantity: 1
      }]);
    }
    
    // Update total amount
    const newTotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0) + price;
    setAmount(newTotal.toFixed(2));
  };

  const removeFromCart = (id: string) => {
    const newCart = cart.filter(item => item.id !== id);
    setCart(newCart);
    
    // Update total amount
    const newTotal = newCart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    setAmount(newTotal.toFixed(2));
  };

  const cartTotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);

  const handleThermalPrint = async (receiptData: string, copies: number = 1) => {
    setIsPrinting(true);
    try {
      const result = await paxPosService.printReceipt(receiptData, copies);
      if (result.success) {
        successToast(`Receipt printed successfully (${copies} copies)`);
      } else {
        warningToast(`Thermal printer not available: ${result.message}`);
        // Fallback to browser print
        window.print();
      }
    } catch (error) {
      console.error('Thermal print error:', error);
      errorToast('Failed to print receipt');
    } finally {
      setIsPrinting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={onBack}
            className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
          >
            ← Back
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">PAX POS Terminal</h1>
            <p className="text-sm text-gray-600">
              {terminalStatus?.connected ? 'Connected' : 'Simulation Mode'} • {terminalStatus?.model}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${
            terminalStatus?.connected ? 'bg-green-500' : 'bg-yellow-500'
          }`} />
          <span className="text-sm text-gray-600">
            {terminalStatus?.connected ? 'Connected' : 'Simulation'}
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Payment Section */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-lg font-semibold mb-4">Payment Processing</h2>
          
          <div className="space-y-4">
            <div>
              <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-2">
                Amount ($)
              </label>
              <input
                ref={amountInputRef}
                id="amount"
                type="number"
                step="0.01"
                min="0"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="0.00"
                disabled={isProcessing}
              />
            </div>

            <div className="flex space-x-3">
              <button
                onClick={handlePayment}
                disabled={isProcessing || !amount || parseFloat(amount) <= 0}
                className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                {isProcessing ? 'Processing...' : 'Process Payment'}
              </button>
              
              {isProcessing && (
                <button
                  onClick={handleCancelTransaction}
                  className="bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 transition-colors"
                >
                  Cancel
                </button>
              )}
            </div>

            <button
              onClick={handleTestConnection}
              disabled={isConnecting}
              className="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 disabled:bg-gray-400 transition-colors"
            >
              {isConnecting ? 'Testing...' : 'Test Connection'}
            </button>
          </div>
        </div>

        {/* Terminal Status */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-lg font-semibold mb-4">Terminal Status</h2>
          
          {terminalStatus ? (
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Status:</span>
                <span className={`font-medium ${
                  terminalStatus.connected ? 'text-green-600' : 'text-yellow-600'
                }`}>
                  {terminalStatus.connected ? 'Connected' : 'Disconnected'}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Model:</span>
                <span className="font-medium">{terminalStatus.model}</span>
              </div>
              
              {terminalStatus.ip && (
                <div className="flex justify-between">
                  <span className="text-gray-600">IP Address:</span>
                  <span className="font-medium">{terminalStatus.ip}:{terminalStatus.port}</span>
                </div>
              )}
              
              {terminalStatus.capabilities && (
                <div className="mt-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Capabilities:</h3>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="flex items-center">
                      <span className={`w-2 h-2 rounded-full mr-2 ${
                        terminalStatus.capabilities.contactless ? 'bg-green-500' : 'bg-gray-300'
                      }`} />
                      Contactless
                    </div>
                    <div className="flex items-center">
                      <span className={`w-2 h-2 rounded-full mr-2 ${
                        terminalStatus.capabilities.emv ? 'bg-green-500' : 'bg-gray-300'
                      }`} />
                      EMV Chip
                    </div>
                    <div className="flex items-center">
                      <span className={`w-2 h-2 rounded-full mr-2 ${
                        terminalStatus.capabilities.magneticStripe ? 'bg-green-500' : 'bg-gray-300'
                      }`} />
                      Mag Stripe
                    </div>
                    <div className="flex items-center">
                      <span className={`w-2 h-2 rounded-full mr-2 ${
                        terminalStatus.capabilities.printer ? 'bg-green-500' : 'bg-gray-300'
                      }`} />
                      Printer
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center text-gray-500">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
              Loading terminal status...
            </div>
          )}
        </div>
      </div>

      {/* Quick Sale Items */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-lg font-semibold mb-4">Quick Sale Items</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {[
            { name: 'Coffee', price: 3.50 },
            { name: 'Sandwich', price: 8.99 },
            { name: 'Snack', price: 2.25 },
            { name: 'Drink', price: 1.99 },
          ].map((item) => (
            <button
              key={item.name}
              onClick={() => addToCart(item.name, item.price)}
              disabled={isProcessing}
              className="p-3 border border-gray-300 rounded-md hover:bg-gray-50 disabled:bg-gray-100 transition-colors"
            >
              <div className="text-sm font-medium">{item.name}</div>
              <div className="text-lg font-bold text-blue-600">${item.price.toFixed(2)}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Shopping Cart */}
      {cart.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-lg font-semibold mb-4">Shopping Cart</h2>
          <div className="space-y-2">
            {cart.map((item) => (
              <div key={item.id} className="flex items-center justify-between py-2 border-b">
                <div className="flex-1">
                  <span className="font-medium">{item.name}</span>
                  <span className="text-gray-600 ml-2">x{item.quantity}</span>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="font-medium">${(item.price * item.quantity).toFixed(2)}</span>
                  <button
                    onClick={() => removeFromCart(item.id)}
                    className="text-red-600 hover:text-red-800"
                  >
                    Remove
                  </button>
                </div>
              </div>
            ))}
            <div className="flex justify-between items-center pt-3 border-t font-bold text-lg">
              <span>Total:</span>
              <span>${cartTotal.toFixed(2)}</span>
            </div>
          </div>
        </div>
      )}

      {/* Last Transaction */}
      {lastTransaction && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-lg font-semibold mb-4">Last Transaction</h2>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">Status:</span>
              <span className={`font-medium ${
                lastTransaction.success ? 'text-green-600' : 'text-red-600'
              }`}>
                {lastTransaction.success ? 'Success' : 'Failed'}
              </span>
            </div>
            {lastTransaction.transactionId && (
              <div className="flex justify-between">
                <span className="text-gray-600">Transaction ID:</span>
                <span className="font-medium">{lastTransaction.transactionId}</span>
              </div>
            )}
            {lastTransaction.authCode && (
              <div className="flex justify-between">
                <span className="text-gray-600">Auth Code:</span>
                <span className="font-medium">{lastTransaction.authCode}</span>
              </div>
            )}
            <div className="flex justify-between">
              <span className="text-gray-600">Message:</span>
              <span className="font-medium">{lastTransaction.message}</span>
            </div>

            {/* Receipt Actions */}
            {lastTransaction.success && lastTransaction.receiptData && (
              <div className="mt-4 pt-4 border-t space-y-2">
                <button
                  onClick={() => setShowReceiptModal(true)}
                  className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors"
                >
                  📄 View Receipt
                </button>

                {/* Thermal Print Button (only show if hardware mode) */}
                {paxPosService.isHardwareMode() && (
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleThermalPrint(lastTransaction.receiptData!.customer, 1)}
                      disabled={isPrinting}
                      className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 transition-colors"
                    >
                      {isPrinting ? 'Printing...' : '🖨️ Print Customer'}
                    </button>
                    <button
                      onClick={() => handleThermalPrint(lastTransaction.receiptData!.merchant, 1)}
                      disabled={isPrinting}
                      className="flex-1 bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 disabled:bg-gray-400 transition-colors"
                    >
                      {isPrinting ? 'Printing...' : '🖨️ Print Merchant'}
                    </button>
                  </div>
                )}

                {/* Hardware Status Indicator */}
                <div className="text-xs text-center text-gray-500">
                  {paxPosService.isHardwareMode() ?
                    '🔗 Hardware Mode - Thermal printer available' :
                    '☁️ Simulation Mode - Browser print only'
                  }
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Receipt Modal */}
      {lastTransaction && (
        <PaxReceiptModal
          isOpen={showReceiptModal}
          transaction={lastTransaction}
          onClose={() => setShowReceiptModal(false)}
          showPrintOptions={true}
        />
      )}
    </div>
  );
}
