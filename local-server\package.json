{"name": "pax-pos-local-server", "version": "1.0.0", "description": "Local server for PAX POS hardware integration", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "build": "echo 'No build step required'", "test": "echo 'No tests specified'"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "axios": "^1.6.0", "dotenv": "^16.3.1", "helmet": "^7.1.0", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["pax", "pos", "payment", "terminal", "hardware"], "author": "", "license": "MIT"}