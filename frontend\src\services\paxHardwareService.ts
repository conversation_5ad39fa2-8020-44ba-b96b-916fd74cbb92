/**
 * PAX Hardware Service
 * Direct integration with PAX C# DLL for local hardware communication
 * This service runs a local Node.js bridge to communicate with the C# libraries
 */

import { env } from '../config/env';

export interface PaxHardwareConfig {
  dllPath: string;
  terminalIP: string;
  terminalPort: number;
  timeout: number;
  merchantId: string;
  authUrl: string;
}

export interface PaxHardwarePaymentRequest {
  amount: number; // Amount in cents
  tenderType: 'CREDIT' | 'DEBIT' | 'CASH';
  transType: 'SALE' | 'REFUND' | 'VOID';
  referenceNumber?: string;
  items?: Array<{
    name: string;
    price: number;
    quantity: number;
  }>;
}

export interface PaxHardwarePaymentResponse {
  success: boolean;
  transactionId?: string;
  authCode?: string;
  resultCode?: string;
  message?: string;
  receiptData?: string;
  cardInfo?: {
    last4?: string;
    brand?: string;
    entryMethod?: string;
  };
  rawResponse?: any;
}

export interface PaxHardwareStatus {
  connected: boolean;
  dllLoaded: boolean;
  terminalReachable: boolean;
  lastError?: string;
  capabilities?: {
    contactless: boolean;
    emv: boolean;
    magneticStripe: boolean;
    printer: boolean;
  };
}

class PaxHardwareService {
  private bridgePort: number = 3002;
  private bridgeUrl: string;
  private isInitialized: boolean = false;
  private bridgeProcess: any = null;

  constructor() {
    this.bridgeUrl = `http://localhost:${this.bridgePort}`;
  }

  /**
   * Initialize the PAX hardware service
   * This starts a local Node.js bridge process that loads the C# DLL
   */
  async initialize(): Promise<boolean> {
    try {
      // Check if we're in a browser environment
      if (typeof window === 'undefined') {
        throw new Error('PAX Hardware Service can only run in browser environment');
      }

      // Check if running on Windows (required for C# DLL)
      if (!this.isWindowsEnvironment()) {
        console.warn('PAX Hardware Service requires Windows environment for C# DLL integration');
        return false;
      }

      // Start the bridge service
      await this.startBridgeService();
      
      // Test connection to bridge
      const status = await this.getHardwareStatus();
      this.isInitialized = status.connected;

      return this.isInitialized;
    } catch (error) {
      console.error('Failed to initialize PAX Hardware Service:', error);
      return false;
    }
  }

  /**
   * Check if running on Windows
   */
  private isWindowsEnvironment(): boolean {
    return navigator.platform.toLowerCase().includes('win');
  }

  /**
   * Start the local bridge service
   */
  private async startBridgeService(): Promise<void> {
    try {
      // Check if bridge is already running
      const isRunning = await this.checkBridgeStatus();
      if (isRunning) {
        console.log('PAX bridge service already running');
        return;
      }

      // Start the bridge service using Electron or Node.js child process
      if (this.isElectronEnvironment()) {
        await this.startElectronBridge();
      } else {
        await this.startWebBridge();
      }

    } catch (error) {
      throw new Error(`Failed to start bridge service: ${error.message}`);
    }
  }

  /**
   * Check if running in Electron environment
   */
  private isElectronEnvironment(): boolean {
    return !!(window as any).require;
  }

  /**
   * Start bridge in Electron environment
   */
  private async startElectronBridge(): Promise<void> {
    const { spawn } = (window as any).require('child_process');
    const path = (window as any).require('path');

    const bridgeScript = path.join(process.cwd(), 'frontend', 'src', 'services', 'paxBridge.js');
    
    this.bridgeProcess = spawn('node', [bridgeScript, '--port', this.bridgePort.toString()], {
      stdio: 'pipe',
      detached: false
    });

    // Wait for bridge to start
    await new Promise((resolve, reject) => {
      const timeout = setTimeout(() => reject(new Error('Bridge startup timeout')), 10000);
      
      const checkStartup = async () => {
        try {
          const isRunning = await this.checkBridgeStatus();
          if (isRunning) {
            clearTimeout(timeout);
            resolve(true);
          } else {
            setTimeout(checkStartup, 500);
          }
        } catch (error) {
          setTimeout(checkStartup, 500);
        }
      };

      checkStartup();
    });
  }

  /**
   * Start bridge in web environment (requires user to manually start bridge)
   */
  private async startWebBridge(): Promise<void> {
    // In web environment, we can't start processes directly
    // User needs to manually start the bridge service
    const isRunning = await this.checkBridgeStatus();
    if (!isRunning) {
      throw new Error(
        'PAX Bridge Service not running. Please start it manually:\n' +
        'cd frontend && node src/services/paxBridge.js'
      );
    }
  }

  /**
   * Check if bridge service is running
   */
  private async checkBridgeStatus(): Promise<boolean> {
    try {
      const response = await fetch(`${this.bridgeUrl}/health`, {
        method: 'GET',
        timeout: 5000,
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  /**
   * Process payment through hardware
   */
  async processPayment(request: PaxHardwarePaymentRequest): Promise<PaxHardwarePaymentResponse> {
    if (!this.isInitialized) {
      throw new Error('PAX Hardware Service not initialized');
    }

    try {
      const response = await fetch(`${this.bridgeUrl}/api/payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: request.amount,
          tenderType: request.tenderType,
          transType: request.transType,
          referenceNumber: request.referenceNumber || this.generateReferenceNumber(),
          items: request.items || []
        }),
      });

      if (!response.ok) {
        throw new Error(`Payment request failed: ${response.statusText}`);
      }

      const result = await response.json();
      return this.parsePaymentResponse(result);

    } catch (error) {
      console.error('PAX hardware payment error:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Payment processing failed'
      };
    }
  }

  /**
   * Get hardware status
   */
  async getHardwareStatus(): Promise<PaxHardwareStatus> {
    try {
      const response = await fetch(`${this.bridgeUrl}/api/status`, {
        method: 'GET',
      });

      if (!response.ok) {
        return {
          connected: false,
          dllLoaded: false,
          terminalReachable: false,
          lastError: `Bridge communication failed: ${response.statusText}`
        };
      }

      const result = await response.json();
      return {
        connected: result.success,
        dllLoaded: result.data?.dllLoaded || false,
        terminalReachable: result.data?.terminalReachable || false,
        capabilities: result.data?.capabilities,
        lastError: result.error
      };

    } catch (error) {
      return {
        connected: false,
        dllLoaded: false,
        terminalReachable: false,
        lastError: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Test terminal connection
   */
  async testConnection(): Promise<{ success: boolean; message: string }> {
    try {
      const response = await fetch(`${this.bridgeUrl}/api/test`, {
        method: 'POST',
      });

      const result = await response.json();
      return {
        success: result.success,
        message: result.message || (result.success ? 'Connection successful' : 'Connection failed')
      };

    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Connection test failed'
      };
    }
  }

  /**
   * Cancel current transaction
   */
  async cancelTransaction(): Promise<{ success: boolean; message: string }> {
    try {
      const response = await fetch(`${this.bridgeUrl}/api/cancel`, {
        method: 'POST',
      });

      const result = await response.json();
      return {
        success: result.success,
        message: result.message || (result.success ? 'Transaction cancelled' : 'Cancel failed')
      };

    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Cancel failed'
      };
    }
  }

  /**
   * Print receipt directly to thermal printer
   */
  async printReceipt(receiptData: string, copies: number = 1): Promise<{ success: boolean; message: string }> {
    try {
      const response = await fetch(`${this.bridgeUrl}/api/print`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          receiptData,
          copies
        }),
      });

      const result = await response.json();
      return {
        success: result.success,
        message: result.message || (result.success ? 'Receipt printed' : 'Print failed')
      };

    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Print failed'
      };
    }
  }

  /**
   * Cleanup and shutdown
   */
  async shutdown(): Promise<void> {
    try {
      if (this.bridgeProcess) {
        this.bridgeProcess.kill();
        this.bridgeProcess = null;
      }
      this.isInitialized = false;
    } catch (error) {
      console.error('Error during shutdown:', error);
    }
  }

  /**
   * Check if hardware service is available
   */
  isAvailable(): boolean {
    return this.isInitialized && this.isWindowsEnvironment();
  }

  /**
   * Parse payment response from bridge
   */
  private parsePaymentResponse(result: any): PaxHardwarePaymentResponse {
    return {
      success: result.success,
      transactionId: result.data?.transactionId,
      authCode: result.data?.authCode,
      resultCode: result.data?.resultCode,
      message: result.message,
      receiptData: result.data?.receiptData,
      cardInfo: result.data?.cardInfo,
      rawResponse: result.data?.rawResponse
    };
  }

  /**
   * Generate unique reference number
   */
  private generateReferenceNumber(): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `HW${timestamp}${random}`;
  }
}

// Export singleton instance
export const paxHardwareService = new PaxHardwareService();
export default paxHardwareService;
