{"name": "frontend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "start": "vite preview --port 3000", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest run", "bridge": "node src/services/paxBridge.js", "bridge:install": "npm install express cors edge-js", "bridge:start": "npm run bridge:install && npm run bridge"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.5.0", "@stripe/terminal-js": "^0.24.0", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@types/node": "^20.12.7", "@types/qrcode.react": "^1.0.5", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "axios": "^1.6.8", "lucide-react": "^0.539.0", "postcss": "^8.4.38", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.51.2", "react-hot-toast": "^2.5.2", "react-router-dom": "^6.22.3", "sonner": "^2.0.7", "tailwindcss": "^3.4.3", "typescript": "^5.4.5", "vite": "^5.2.0", "zod": "^3.22.4", "zustand": "^4.5.2"}, "devDependencies": {"@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "eslint": "^8.57.0", "jsdom": "^24.0.0", "vitest": "^1.5.0"}, "optionalDependencies": {"express": "^4.18.2", "cors": "^2.8.5", "edge-js": "^20.10.0"}}