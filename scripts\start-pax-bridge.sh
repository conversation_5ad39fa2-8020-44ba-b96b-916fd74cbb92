#!/bin/bash

# PAX Hardware Bridge Startup Script (Linux/Mac)
# This script starts the Node.js bridge service for PAX hardware integration

echo "========================================"
echo "    PAX Hardware Bridge Startup"
echo "========================================"
echo

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js is not installed or not in PATH"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

echo "Node.js version:"
node --version
echo

# Navigate to frontend directory
cd "$(dirname "$0")/../frontend"

# Check if bridge dependencies are installed
if [ ! -d "node_modules/express" ]; then
    echo "Installing bridge dependencies..."
    cp bridge-package.json package-bridge.json
    npm install express cors edge-js
    echo
fi

# Check if PAX DLL exists (Note: This won't work on Linux/Mac)
if [ ! -f "public/libs/PaxWrapperSDK.dll" ]; then
    echo "WARNING: PAX DLL not found at public/libs/PaxWrapperSDK.dll"
    echo "Note: PAX hardware integration only works on Windows"
    echo "The bridge will run in simulation mode"
    echo
else
    echo "PAX DLL found: public/libs/PaxWrapperSDK.dll"
    echo
fi

# Set environment variables
export PAX_TERMINAL_IP=${PAX_TERMINAL_IP:-**************}
export PAX_TERMINAL_PORT=${PAX_TERMINAL_PORT:-10009}
export PAX_TIMEOUT=${PAX_TIMEOUT:-90}
export PAX_MERCHANT_ID=${PAX_MERCHANT_ID:-MERCHANT001}

echo "Environment Configuration:"
echo "- Terminal IP: $PAX_TERMINAL_IP"
echo "- Terminal Port: $PAX_TERMINAL_PORT"
echo "- Timeout: ${PAX_TIMEOUT}s"
echo "- Merchant ID: $PAX_MERCHANT_ID"
echo

echo "Starting PAX Hardware Bridge on port 3002..."
echo "Press Ctrl+C to stop the service"
echo

# Start the bridge service
node src/services/paxBridge.js --port 3002

echo
echo "PAX Hardware Bridge stopped."
