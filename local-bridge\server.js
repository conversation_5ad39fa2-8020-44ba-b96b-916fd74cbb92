const express = require('express');
const cors = require('cors');
const ffi = require('ffi-napi');
const ref = require('ref-napi');
const path = require('path');

const app = express();
const PORT = 3002;

// Enable CORS for Vercel deployment
app.use(cors({
  origin: ['http://localhost:3000', 'https://*.vercel.app'],
  credentials: true
}));

app.use(express.json());

// PAX SDK Configuration
const PAX_CONFIG = {
  terminalIP: '**************',
  terminalPort: 10009,
  timeout: 90,
  merchantId: 'MERCHANT001'
};

// Load PAX SDK DLL
let paxSDK = null;
try {
  const dllPath = path.join(__dirname, 'libs', 'PaxWrapperSDK.dll');
  paxSDK = ffi.Library(dllPath, {
    'InitializeTerminal': ['int', ['string', 'int']],
    'ProcessPayment': ['string', ['double', 'string']],
    'PrintReceipt': ['int', ['string']],
    'GetTerminalStatus': ['string', []],
    'CloseConnection': ['int', []]
  });
  console.log('PAX SDK loaded successfully');
} catch (error) {
  console.error('Failed to load PAX SDK:', error.message);
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    paxSDKLoaded: !!paxSDK,
    terminalIP: PAX_CONFIG.terminalIP
  });
});

// Initialize terminal connection
app.post('/initialize', async (req, res) => {
  try {
    if (!paxSDK) {
      return res.status(500).json({ 
        success: false, 
        error: 'PAX SDK not loaded' 
      });
    }

    const result = paxSDK.InitializeTerminal(
      PAX_CONFIG.terminalIP, 
      PAX_CONFIG.terminalPort
    );

    if (result === 0) {
      res.json({ 
        success: true, 
        message: 'Terminal initialized successfully',
        terminalIP: PAX_CONFIG.terminalIP
      });
    } else {
      res.status(500).json({ 
        success: false, 
        error: `Initialization failed with code: ${result}` 
      });
    }
  } catch (error) {
    console.error('Initialize error:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});

// Process payment
app.post('/payment', async (req, res) => {
  try {
    const { amount, transactionId } = req.body;

    if (!paxSDK) {
      return res.status(500).json({ 
        success: false, 
        error: 'PAX SDK not loaded' 
      });
    }

    if (!amount || amount <= 0) {
      return res.status(400).json({ 
        success: false, 
        error: 'Invalid amount' 
      });
    }

    console.log(`Processing payment: £${amount} for transaction ${transactionId}`);

    const result = paxSDK.ProcessPayment(amount, transactionId || '');
    const response = JSON.parse(result);

    if (response.success) {
      res.json({
        success: true,
        transactionId: response.transactionId,
        amount: amount,
        cardType: response.cardType,
        last4: response.last4,
        authCode: response.authCode,
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(400).json({
        success: false,
        error: response.error || 'Payment failed'
      });
    }
  } catch (error) {
    console.error('Payment error:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});

// Print receipt
app.post('/print-receipt', async (req, res) => {
  try {
    const { receiptData } = req.body;

    if (!paxSDK) {
      return res.status(500).json({ 
        success: false, 
        error: 'PAX SDK not loaded' 
      });
    }

    if (!receiptData) {
      return res.status(400).json({ 
        success: false, 
        error: 'Receipt data required' 
      });
    }

    const result = paxSDK.PrintReceipt(JSON.stringify(receiptData));

    if (result === 0) {
      res.json({ 
        success: true, 
        message: 'Receipt printed successfully' 
      });
    } else {
      res.status(500).json({ 
        success: false, 
        error: `Print failed with code: ${result}` 
      });
    }
  } catch (error) {
    console.error('Print error:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});

// Get terminal status
app.get('/status', async (req, res) => {
  try {
    if (!paxSDK) {
      return res.status(500).json({ 
        success: false, 
        error: 'PAX SDK not loaded' 
      });
    }

    const statusResult = paxSDK.GetTerminalStatus();
    const status = JSON.parse(statusResult);

    res.json({
      success: true,
      status: status,
      terminalIP: PAX_CONFIG.terminalIP,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Status error:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('Shutting down bridge server...');
  if (paxSDK) {
    try {
      paxSDK.CloseConnection();
      console.log('PAX connection closed');
    } catch (error) {
      console.error('Error closing PAX connection:', error);
    }
  }
  process.exit(0);
});

app.listen(PORT, () => {
  console.log(`PAX Bridge Server running on port ${PORT}`);
  console.log(`Terminal IP: ${PAX_CONFIG.terminalIP}`);
  console.log(`Health check: http://localhost:${PORT}/health`);
});
