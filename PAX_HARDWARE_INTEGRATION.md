# PAX Hardware Integration Guide

## 🔧 Overview

The PAX hardware integration provides direct communication with PAX A920 terminals through C# DLL libraries. This enables real hardware payment processing and thermal receipt printing when running locally on Windows.

## 🏗️ Architecture

### Hybrid Architecture
```
Frontend (React) 
    ↓
PAX POS Service (TypeScript)
    ↓
┌─────────────────┬─────────────────┐
│  Hardware Mode  │  Simulation Mode │
│                 │                  │
│ PAX Hardware    │ Backend API      │
│ Service         │ Service          │
│     ↓           │     ↓            │
│ Node.js Bridge  │ Cloud/Simulation │
│     ↓           │                  │
│ C# DLL          │                  │
│     ↓           │                  │
│ PAX Terminal    │                  │
└─────────────────┴─────────────────┘
```

### Components

1. **PAX POS Service** (`frontend/src/services/paxPosService.ts`)
   - Main service interface
   - Automatically detects hardware vs simulation mode
   - Unified API for both modes

2. **PAX Hardware Service** (`frontend/src/services/paxHardwareService.ts`)
   - Direct hardware communication
   - Manages Node.js bridge process
   - Windows-only functionality

3. **PAX Bridge** (`frontend/src/services/paxBridge.js`)
   - Node.js service that loads C# DLL
   - HTTP API for frontend communication
   - Handles edge-js integration

4. **C# Libraries** (`frontend/public/libs/`)
   - PAX SDK DLLs
   - System dependencies
   - Hardware communication layer

## 🚀 Quick Start

### Prerequisites
- Windows operating system (for hardware mode)
- Node.js 16+ installed
- PAX A920 terminal connected to network
- C# libraries copied to `frontend/public/libs/`

### 1. Start the Hardware Bridge
```bash
# Windows
scripts\start-pax-bridge.bat

# Linux/Mac (simulation only)
scripts/start-pax-bridge.sh
```

### 2. Start the Frontend
```bash
cd frontend
npm run dev
```

### 3. Verify Integration
- Open the frontend application
- Navigate to PAX POS Terminal
- Check the status indicator (Hardware Mode vs Simulation Mode)

## 🔧 Configuration

### Environment Variables
```bash
# Terminal Configuration
PAX_TERMINAL_IP=**************
PAX_TERMINAL_PORT=10009
PAX_TIMEOUT=90

# Merchant Configuration
PAX_MERCHANT_ID=MERCHANT001
PAX_MERCHANT_NAME="Your Business Name"

# Bridge Configuration
PAX_BRIDGE_PORT=3002
```

### Terminal Network Setup
1. Connect PAX A920 to your network
2. Configure terminal IP address
3. Ensure port 10009 is accessible
4. Test connectivity with ping

## 💳 Payment Processing

### Hardware Mode Features
- ✅ Real card processing
- ✅ EMV chip transactions
- ✅ Contactless payments
- ✅ Magnetic stripe fallback
- ✅ Thermal receipt printing
- ✅ Real-time terminal status

### Simulation Mode Features
- ✅ Simulated transactions (90% success rate)
- ✅ Digital receipts
- ✅ Browser-based printing
- ✅ Full UI functionality
- ✅ Cloud deployment compatible

## 🧾 Receipt Printing

### Thermal Printer (Hardware Mode)
```typescript
// Print customer receipt
await paxPosService.printReceipt(receiptData, 1);

// Print multiple copies
await paxPosService.printReceipt(receiptData, 2);
```

### Browser Printing (Simulation Mode)
- Automatic fallback to browser print dialog
- Print-optimized CSS styling
- Thermal printer formatting

## 🔍 Status Monitoring

### Hardware Status Check
```typescript
const status = await paxPosService.getTerminalStatus();
console.log('Connected:', status.connected);
console.log('Mode:', paxPosService.isHardwareMode() ? 'Hardware' : 'Simulation');
```

### Bridge Health Check
```bash
curl http://localhost:3002/health
```

## 🛠️ Development

### Local Development Setup
1. **Install Dependencies**
   ```bash
   cd frontend
   npm install express cors edge-js
   ```

2. **Copy PAX Libraries**
   ```bash
   copy "PaxPos-Integration\libs\*" "frontend\public\libs\"
   ```

3. **Start Bridge Service**
   ```bash
   node frontend/src/services/paxBridge.js
   ```

4. **Start Frontend**
   ```bash
   cd frontend && npm run dev
   ```

### Testing Hardware Integration
```bash
# Test bridge connectivity
curl http://localhost:3002/api/status

# Test payment processing
curl -X POST http://localhost:3002/api/payment \
  -H "Content-Type: application/json" \
  -d '{"amount": 1000, "tenderType": "CREDIT"}'
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Bridge Service Won't Start
**Problem**: `edge-js` installation fails
**Solution**: 
```bash
npm install --global windows-build-tools
npm install edge-js
```

#### 2. DLL Not Found
**Problem**: `PAX Wrapper SDK not found`
**Solution**: 
- Verify DLL exists at `frontend/public/libs/PaxWrapperSDK.dll`
- Check file permissions
- Ensure all dependencies are copied

#### 3. Terminal Connection Failed
**Problem**: Cannot connect to PAX terminal
**Solution**:
- Verify terminal IP address
- Check network connectivity
- Ensure port 10009 is open
- Test with ping command

#### 4. Payment Processing Fails
**Problem**: Transactions return error codes
**Solution**:
- Check terminal is signed on
- Verify merchant configuration
- Review terminal logs
- Test with smaller amounts

### Debug Mode
Enable detailed logging:
```bash
set DEBUG=pax:*
node frontend/src/services/paxBridge.js
```

## 🌐 Deployment Modes

### Local Hardware Deployment
- Windows machine with PAX terminal
- Bridge service runs locally
- Full hardware functionality
- Thermal printing available

### Cloud Deployment (Render/Heroku)
- Automatic simulation mode
- No hardware dependencies
- Browser-based printing
- Full UI functionality

### Hybrid Deployment
- Local hardware for main terminal
- Cloud backup for remote access
- Data synchronization
- Failover capabilities

## 📊 Performance Considerations

### Hardware Mode
- **Payment Processing**: 3-10 seconds (depends on card type)
- **Receipt Printing**: 1-3 seconds
- **Status Checks**: <1 second
- **Memory Usage**: ~50MB (bridge service)

### Simulation Mode
- **Payment Processing**: 2 seconds (simulated delay)
- **Receipt Generation**: <1 second
- **Status Checks**: <1 second
- **Memory Usage**: Minimal

## 🔒 Security

### Hardware Mode Security
- Local network communication only
- No sensitive data stored in browser
- DLL-level encryption
- Terminal-level security

### Data Protection
- Transaction data encrypted in transit
- No card data stored locally
- PCI DSS compliance maintained
- Secure receipt handling

## 📈 Monitoring and Logging

### Bridge Service Logs
```bash
# View real-time logs
tail -f bridge.log

# Check error logs
grep ERROR bridge.log
```

### Frontend Monitoring
- Browser console for client-side errors
- Network tab for API communication
- Performance monitoring for response times

## 🚀 Production Deployment

### Hardware Production Setup
1. **Secure Network Configuration**
   - Dedicated VLAN for POS traffic
   - Firewall rules for port 10009
   - VPN access for remote management

2. **Service Management**
   - Windows Service wrapper for bridge
   - Automatic startup configuration
   - Health monitoring and alerts

3. **Backup and Recovery**
   - Transaction log backup
   - Configuration backup
   - Disaster recovery procedures

### Monitoring and Alerts
- Terminal connectivity monitoring
- Transaction success rate tracking
- Error rate alerting
- Performance metrics

## 📞 Support

### Hardware Issues
- Check PAX terminal documentation
- Verify network connectivity
- Review DLL compatibility
- Contact PAX support if needed

### Software Issues
- Check browser console for errors
- Verify bridge service status
- Review Node.js logs
- Test with simulation mode

### Integration Issues
- Verify all dependencies installed
- Check file permissions
- Test API endpoints individually
- Review configuration settings
