#!/usr/bin/env node

/**
 * PAX Hardware Bridge Service
 * Node.js service that loads C# DLL and provides HTTP API for frontend
 * This runs as a separate process to handle hardware communication
 */

const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

// Try to load edge-js for C# DLL integration
let edge;
try {
  edge = require('edge-js');
} catch (error) {
  console.error('edge-js not found. Installing...');
  require('child_process').execSync('npm install edge-js', { stdio: 'inherit' });
  edge = require('edge-js');
}

class PaxBridge {
  constructor(port = 3002) {
    this.port = port;
    this.app = express();
    this.dllLoaded = false;
    this.paxFunctions = {};
    this.config = {
      dllPath: path.join(__dirname, '../../public/libs/PaxWrapperSDK.dll'),
      terminalIP: process.env.PAX_TERMINAL_IP || '**************',
      terminalPort: parseInt(process.env.PAX_TERMINAL_PORT) || 10009,
      timeout: parseInt(process.env.PAX_TIMEOUT) || 90,
      authUrl: process.env.PAX_AUTH_URL || '',
      merchantId: process.env.PAX_MERCHANT_ID || 'MERCHANT001'
    };
    
    this.setupMiddleware();
    this.setupRoutes();
    this.loadDLL();
  }

  setupMiddleware() {
    this.app.use(cors({
      origin: ['http://localhost:3000', 'http://localhost:3001'],
      credentials: true
    }));
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));
    
    // Logging middleware
    this.app.use((req, res, next) => {
      console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
      next();
    });
  }

  setupRoutes() {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({
        success: true,
        service: 'PAX Hardware Bridge',
        dllLoaded: this.dllLoaded,
        timestamp: new Date().toISOString()
      });
    });

    // Get status
    this.app.get('/api/status', async (req, res) => {
      try {
        const status = await this.getTerminalStatus();
        res.json({
          success: true,
          data: {
            dllLoaded: this.dllLoaded,
            terminalReachable: status.connected,
            capabilities: {
              contactless: true,
              emv: true,
              magneticStripe: true,
              printer: true
            },
            config: {
              terminalIP: this.config.terminalIP,
              terminalPort: this.config.terminalPort
            }
          }
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });

    // Process payment
    this.app.post('/api/payment', async (req, res) => {
      try {
        if (!this.dllLoaded) {
          throw new Error('PAX DLL not loaded');
        }

        const { amount, tenderType, transType, referenceNumber, items } = req.body;
        
        const result = await this.processPayment({
          amount,
          tenderType: tenderType || 'CREDIT',
          transType: transType || 'SALE',
          referenceNumber: referenceNumber || this.generateReferenceNumber(),
          items: items || []
        });

        res.json({
          success: result.success,
          data: result.success ? {
            transactionId: result.transactionId,
            authCode: result.authCode,
            resultCode: result.resultCode,
            receiptData: result.receiptData,
            cardInfo: result.cardInfo,
            rawResponse: result.rawResponse
          } : undefined,
          message: result.message,
          error: result.success ? undefined : result.message
        });

      } catch (error) {
        console.error('Payment processing error:', error);
        res.status(500).json({
          success: false,
          error: error.message
        });
      }
    });

    // Test connection
    this.app.post('/api/test', async (req, res) => {
      try {
        const result = await this.testConnection();
        res.json({
          success: result.success,
          message: result.message
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          message: error.message
        });
      }
    });

    // Cancel transaction
    this.app.post('/api/cancel', async (req, res) => {
      try {
        const result = await this.cancelTransaction();
        res.json({
          success: result.success,
          message: result.message
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          message: error.message
        });
      }
    });

    // Print receipt
    this.app.post('/api/print', async (req, res) => {
      try {
        const { receiptData, copies = 1 } = req.body;
        const result = await this.printReceipt(receiptData, copies);
        res.json({
          success: result.success,
          message: result.message
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          message: error.message
        });
      }
    });
  }

  loadDLL() {
    try {
      console.log(`Loading PAX DLL from: ${this.config.dllPath}`);
      
      if (!fs.existsSync(this.config.dllPath)) {
        throw new Error(`PAX DLL not found at: ${this.config.dllPath}`);
      }

      // Create Edge.js functions for PAX operations
      this.paxFunctions = {
        ProcessTrans: this.createEdgeFunction('ProcessTrans'),
        CancelTrans: this.createEdgeFunction('CancelTrans'),
        SignOnPOS: this.createEdgeFunction('SignOnPOS'),
        DoSign: this.createEdgeFunction('DoSign'),
        GetSign: this.createEdgeFunction('GetSign')
      };

      this.dllLoaded = true;
      console.log('PAX DLL loaded successfully');

    } catch (error) {
      console.error('Failed to load PAX DLL:', error.message);
      this.dllLoaded = false;
    }
  }

  createEdgeFunction(methodName) {
    try {
      return edge.func({
        assemblyFile: path.resolve(this.config.dllPath),
        typeName: 'MoleQ.Integration.PaxWrapperSdk.PayApi',
        methodName: methodName
      });
    } catch (error) {
      throw new Error(`Failed to create Edge function for ${methodName}: ${error.message}`);
    }
  }

  async processPayment(paymentData) {
    return new Promise((resolve) => {
      if (!this.dllLoaded || !this.paxFunctions.ProcessTrans) {
        resolve({
          success: false,
          message: 'PAX DLL not available'
        });
        return;
      }

      const transactionRequest = {
        AuthUrl: this.config.authUrl,
        TenderType: paymentData.tenderType,
        TransType: paymentData.transType,
        Amount: paymentData.amount,
        ECRRefNum: paymentData.referenceNumber,
        ReportStatus: false,
        ip: this.config.terminalIP,
        port: this.config.terminalPort,
        timeout: this.config.timeout
      };

      console.log('Processing payment:', transactionRequest);

      this.paxFunctions.ProcessTrans(JSON.stringify(transactionRequest), (error, result) => {
        if (error) {
          console.error('Payment processing error:', error);
          resolve({
            success: false,
            message: error.message || 'Payment processing failed'
          });
          return;
        }

        try {
          const response = typeof result === 'string' ? JSON.parse(result) : result;
          console.log('Payment response:', response);

          resolve({
            success: response.ResultCode === '000000' || response.ResultCode === '00',
            transactionId: response.TransactionId || response.ECRRefNum,
            authCode: response.AuthCode,
            resultCode: response.ResultCode,
            message: response.ResultTxt || response.Message || 'Payment processed',
            receiptData: this.generateReceipt(paymentData, response),
            cardInfo: {
              last4: response.CardLast4 || '****',
              brand: response.CardType || 'UNKNOWN',
              entryMethod: response.EntryMethod || 'UNKNOWN'
            },
            rawResponse: response
          });

        } catch (parseError) {
          console.error('Error parsing payment response:', parseError);
          resolve({
            success: false,
            message: 'Failed to parse payment response'
          });
        }
      });
    });
  }

  async testConnection() {
    return new Promise((resolve) => {
      if (!this.dllLoaded) {
        resolve({
          success: false,
          message: 'PAX DLL not loaded'
        });
        return;
      }

      // Simple ping test
      const testRequest = {
        ip: this.config.terminalIP,
        port: this.config.terminalPort,
        timeout: 10
      };

      // For now, simulate a connection test
      // In a real implementation, you'd use a specific DLL method for testing
      setTimeout(() => {
        resolve({
          success: true,
          message: `Connection test successful to ${this.config.terminalIP}:${this.config.terminalPort}`
        });
      }, 1000);
    });
  }

  async cancelTransaction() {
    return new Promise((resolve) => {
      if (!this.dllLoaded || !this.paxFunctions.CancelTrans) {
        resolve({
          success: false,
          message: 'PAX DLL not available'
        });
        return;
      }

      this.paxFunctions.CancelTrans(null, (error, result) => {
        if (error) {
          resolve({
            success: false,
            message: error.message || 'Cancel failed'
          });
          return;
        }

        resolve({
          success: true,
          message: 'Transaction cancelled successfully'
        });
      });
    });
  }

  async printReceipt(receiptData, copies = 1) {
    // For now, simulate receipt printing
    // In a real implementation, you'd use the PAX printer functions
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log(`Printing ${copies} copies of receipt:`, receiptData);
        resolve({
          success: true,
          message: `Receipt printed (${copies} copies)`
        });
      }, 500);
    });
  }

  async getTerminalStatus() {
    // Simulate terminal status check
    return {
      connected: this.dllLoaded,
      model: 'PAX A920',
      serialNumber: 'HW123456789'
    };
  }

  generateReceipt(paymentData, response) {
    const timestamp = new Date().toLocaleString();
    const amount = (paymentData.amount / 100).toFixed(2);
    
    return `
=============================
        PAXSOFT POS
      CUSTOMER COPY
=============================
Date: ${timestamp}
Transaction ID: ${response.TransactionId || 'N/A'}
Auth Code: ${response.AuthCode || 'N/A'}
=============================
Amount: $${amount}
Type: ${paymentData.transType}
Tender: ${paymentData.tenderType}
Card: ${response.CardType || 'UNKNOWN'} ****${response.CardLast4 || '****'}
=============================
${paymentData.items?.map(item => 
  `${item.name} x${item.quantity} - $${(item.price * item.quantity).toFixed(2)}`
).join('\n') || ''}
=============================
    Thank you!
=============================
    `.trim();
  }

  generateReferenceNumber() {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `HW${timestamp}${random}`;
  }

  start() {
    this.app.listen(this.port, () => {
      console.log(`PAX Hardware Bridge running on port ${this.port}`);
      console.log(`DLL Status: ${this.dllLoaded ? 'Loaded' : 'Failed to load'}`);
      console.log(`Terminal: ${this.config.terminalIP}:${this.config.terminalPort}`);
    });
  }
}

// Handle command line arguments
const args = process.argv.slice(2);
let port = 3002;

for (let i = 0; i < args.length; i++) {
  if (args[i] === '--port' && args[i + 1]) {
    port = parseInt(args[i + 1]);
    i++;
  }
}

// Start the bridge service
const bridge = new PaxBridge(port);
bridge.start();

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\nShutting down PAX Hardware Bridge...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\nShutting down PAX Hardware Bridge...');
  process.exit(0);
});
