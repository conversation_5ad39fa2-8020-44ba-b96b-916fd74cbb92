@echo off
REM PAX Hardware Bridge Startup Script
REM This script starts the Node.js bridge service for PAX hardware integration

echo ========================================
echo    PAX Hardware Bridge Startup
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js version:
node --version
echo.

REM Navigate to frontend directory
cd /d "%~dp0..\frontend"

REM Check if bridge dependencies are installed
if not exist "node_modules\express" (
    echo Installing bridge dependencies...
    copy bridge-package.json package-bridge.json
    npm install --prefix . express cors edge-js
    echo.
)

REM Check if PAX DLL exists
if not exist "public\libs\PaxWrapperSDK.dll" (
    echo ERROR: PAX DLL not found at public\libs\PaxWrapperSDK.dll
    echo Please ensure the PAX libraries are copied to frontend\public\libs\
    pause
    exit /b 1
)

echo PAX DLL found: public\libs\PaxWrapperSDK.dll
echo.

REM Set environment variables
set PAX_TERMINAL_IP=**************
set PAX_TERMINAL_PORT=10009
set PAX_TIMEOUT=90
set PAX_MERCHANT_ID=MERCHANT001

echo Environment Configuration:
echo - Terminal IP: %PAX_TERMINAL_IP%
echo - Terminal Port: %PAX_TERMINAL_PORT%
echo - Timeout: %PAX_TIMEOUT%s
echo - Merchant ID: %PAX_MERCHANT_ID%
echo.

echo Starting PAX Hardware Bridge on port 3002...
echo Press Ctrl+C to stop the service
echo.

REM Start the bridge service
node src\services\paxBridge.js --port 3002

echo.
echo PAX Hardware Bridge stopped.
pause
